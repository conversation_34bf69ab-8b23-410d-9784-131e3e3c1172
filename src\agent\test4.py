from typing_extensions import TypedDict
from langgraph.graph import StateGraph, START, END
import requests
import json

# async def fetch1post(url):
#     headers = {"Authorization": "Bearer f23b4a6ddb354fc28cb77ff9d51e7020-458c26b7352a4e0fb582de430c0523bb",
#                "X-Emp-No"       :   "10306639",
#                "X-Auth-Value": "eb2c3782f01589fc50831f389a69e5d4"}
#     data = {
#         "chatUuid": "efd8a29c12af4be9ba8e324808ffac14",
#         "chatName": "",
#         "stream": "false",
#         "keep": "True",
#         "text": "我的上一个问题是什么",
#         "model": "nebulacoder"
#     }
#     async with aiohttp.ClientSession() as session:
#         async with session.post(url,data=data,headers=headers) as response:
#             text = await response.text()
#             #data = json.loads(text)  # 自行处理解
#             return text

def fetch1post(url,question,number,token,apikey):
    headers = {"Authorization": apikey,
               "X-Emp-No": number,
               "X-Auth-Value": token,
               "Content-Type": "application/json"}
    data = {
        "chatUuid": "",
        "chatName": "",
        "stream": "false",
        "keep": "True",
        "text": question,
        "model": "nebulacoder"
    }
    response = requests.post(url, json=data, headers=headers)
    response.raise_for_status()  # 检查请求是否成功
    return response.text  # 返回响应内容  

class InputState(TypedDict):
    user_input: str
    number: str
    token: str


class OutputState(TypedDict):
    graph_output: str

class OverallState(TypedDict):
    data: str
    number: str
    token: str
    user_input: str
    graph_output: str

class PrivateState(TypedDict):
    bar: str

def node_1(state: InputState) -> OverallState:
    text=fetch1post("https://studio.zte.com.cn/zte-studio-ai-platform/openapi/v1/chat",state["user_input"],state["number"],state["token"],"Bearer ab092c4a74264afd8f9cf6f3186e1ab6-66acf3607e8942d2a2d8296e45c11e7c")   
    jsondata = json.loads(text)
    data = jsondata["bo"]["result"]
    # Write to OverallState
    return {"data": data,"number":state["number"],"token":state["token"]}

def node_2(state: OverallState) -> OverallState:
    # Read from OverallState, write to PrivateState
    text=fetch1post("https://studio.zte.com.cn/zte-studio-ai-platform/openapi/v1/chat",state["data"]+"\r\n"+"陶森10306639",state["number"],state["token"],"Bearer ddc07a1529d844a6bf6a5ca33484fe84-ef09b94ff8154fbb86bc6adb38d668a2")   
    jsondata = json.loads(text)
    print(jsondata)
    #data = jsondata["bo"]["result"]

    return {"data": state["data"]}

def node_3(state: OverallState) -> OutputState:
    # Read from PrivateState, write to OutputState
    return {"graph_output": state["data"]}

builder = StateGraph(OverallState,input_schema=InputState,output_schema=OutputState)
builder.add_node("node_1", node_1)
builder.add_node("node_2", node_2)
builder.add_node("node_3", node_3)
builder.add_edge(START, "node_1")
builder.add_edge("node_1", "node_2")
builder.add_edge("node_2", "node_3")    
builder.add_edge("node_3", END)

graph = builder.compile()
result =graph.invoke({"user_input":"ZXPFM-173921,自动化 陶森10306639","number":"10306639","token":"eb2c3782f01589fc50831f389a69e5d4"})
#print(result)
#print(result["graph_output"])
#{'graph_output': 'My name is Lance'}





