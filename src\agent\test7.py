from langgraph.prebuilt import create_react_agent
from langchain_core.tools import tool
import requests
import json
import aiohttp
import asyncio
# 本地大模型配置
# LOCAL_LLM_CONFIG = {
#     "api_key": "anything",
#     "base_url": "http://10.57.84.90:4000",
#     "model": "myqwen3",
# }
LOCAL_LLM_CONFIG = {
    "api_key": "anything",
    "base_url": "http://10.231.145.183:10900/iask/v8",
    "model": "Qwen3-235B-A22B-FP8",
}
def get_langchain_model():
    """获取配置好的LangChain模型"""
    from langchain_openai import ChatOpenAI
    return ChatOpenAI(
        api_key=LOCAL_LLM_CONFIG["api_key"],
        base_url=LOCAL_LLM_CONFIG["base_url"],
        model=LOCAL_LLM_CONFIG["model"],
        temperature=0.7,
        timeout=600
    )


llm = get_langchain_model()

#工具
# def queryresult(jobid):
#     """获取测试报告结果
#     jobid 用户输入 任务号/任务ID
    
#     返回 测试报告结论 通过/不通过
#     """
#     print("333:",jobid)
#     url = "https://wxiot.zte.com.cn:9100/iworkscript/QueryResult?jobid="+jobid+""
#     response = requests.get(url)
#     print("444:",response.text)

#     response.raise_for_status()  # 检查请求是否成功
#     return response.text  # 返回响应内容  

@tool
async def queryresult(jobid):
    """
    异步获取测试报告结果
    jobid 用户输入 任务号/任务ID
    
    返回 测试报告结论 通过(pass)/不通过(fail)
    """
    # 保留原打印逻辑
    print(f"333: {jobid}")
    
    # 构建请求URL
    url = f"https://wxiot.zte.com.cn:9100/iworkscript/QueryResult?jobid={jobid}"
    
    # 异步HTTP会话管理
    async with aiohttp.ClientSession() as session:
        try:
            # 发送异步GET请求
            async with session.get(url) as response:
                # 读取响应内容
                response_text = await response.text()
                # 保留原打印逻辑
                print(f"444: {response_text}")
                
                # 检查响应状态码
                response.raise_for_status()
                return response_text
        except aiohttp.ClientError as e:
            print(f"HTTP请求错误: {str(e)}")
            return f"查询失败: {str(e)}"
        except Exception as e:
            print(f"处理错误: {str(e)}")
            return f"查询失败: {str(e)}"

agent = create_react_agent(
    model=llm,  
    tools=[queryresult],  
    prompt="你是一个测试报告分析专家，擅长分析测试报告并给出结论"  
)

# Run the agent
async def main():
    result =await agent.ainvoke(
        {"messages": [{"role": "user", "content": "查询19073任务"}]}
    )
    final_response = result['messages'][-1].content
    print(final_response)



if __name__ == "__main__":
    asyncio.run(main())


