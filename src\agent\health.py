from langgraph.prebuilt import create_react_agent
from langchain_core.tools import tool
import requests
import json
import aiohttp
import asyncio
from langchain_core.messages.utils import (
    trim_messages, 
    count_tokens_approximately
)

def pre_model_hook(state):
    trimmed_messages = trim_messages(
        state["messages"],
        strategy="last",
        token_counter=count_tokens_approximately,
        max_tokens=64000,
        start_on="human",
        end_on=("human", "tool"),
    )
    return {"llm_input_messages": trimmed_messages}

LOCAL_LLM_CONFIG = {
    "api_key": "anything",
    "base_url": "http://10.231.145.183:10900/iask/v8",
    "model": "Qwen3-235B-A22B-FP8",
}
def get_langchain_model():
    """获取配置好的LangChain模型"""
    from langchain_openai import ChatOpenAI
    return ChatOpenAI(
        api_key=LOCAL_LLM_CONFIG["api_key"],
        base_url=LOCAL_LLM_CONFIG["base_url"],
        model=LOCAL_LLM_CONFIG["model"],
        temperature=0.7,
        timeout=600
    )


llm = get_langchain_model()

@tool
async def envmonitor():
    """环境监测 

    返回 环境编号 环境名称 环境状态。环境状态字段为reserved03，0代表正常,其余为异常。
    """    
    url = f"https://wxiot.zte.com.cn/iworkscript/zeroquerypoolinfo"
    print   ("333:", url)
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(url) as response:
                response.raise_for_status()
                return await response.text()
        except aiohttp.ClientError as e:
            print(f"请求出错: {str(e)}")
            return None

@tool
async def envmatchversion(version):
    """检测环境是否匹配版本号

    version:目标版本,从用户输入中提取

    返回 环境编号 环境名称 任务ID 是否匹配。1是匹配,0是不匹配.
    """    
    url = f"https://iwork.zx.zte.com.cn/iWork2DataHelper/ZeroVersionMatch?version="+version
    print   ("333:", url)
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(url) as response:
                response.raise_for_status()
                return await response.text()
        except aiohttp.ClientError as e:
            print(f"请求出错: {str(e)}")
            return None

@tool
async def executejob(jobid,number):
    """执行  环境成功匹配 的任务号
    jobid:环境匹配正常的任务号
    number:测试执行人信息 一般为姓名工号 例如陶森10306639
    """    
    url = f"https://iwork.zx.zte.com.cn/iWork2Use/JobJoinPoolServlet?number={number[-8:]}&jobid={jobid}&people={number[:-8]}"
    print   ("333:", url)
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(url) as response:
                response.raise_for_status()
                return await response.text()
        except aiohttp.ClientError as e:
            print(f"请求出错: {str(e)}")
            return None

@tool
async def QueryResult(version):
    """查询 目标版本 的测试结果
    version:目标版本信息
    返回:执行结果,包括通过与不通过机型,不通过机型的主要原因.
    """    
    url = f"https://iwork.zx.zte.com.cn/iWork2DataHelper/ZeroQueryResult?version="+version
    print   ("333:", url)
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(url) as response:
                response.raise_for_status()
                return await response.text()
        except aiohttp.ClientError as e:
            print(f"请求出错: {str(e)}")
            return None


agent7 = create_react_agent(
    model=llm,  
    tools=[envmonitor,envmatchversion,executejob,QueryResult],  
    pre_model_hook=pre_model_hook,
    prompt="你是一个擅长执行测试的人员。"+
    "通常的步骤是检测环境的状态，若异常提示环境负责人，若正常进行接下来的步骤"+
    "进行版本确认,若不符合目标版本则提示，若符合则进行下来的步骤"+
    "执行测试任务，任务执行完成输出执行完成通知，执行过程中遇到问题进行打印输出提示"+
    "任务执行完成后，给出总的测试结论与异常测试点,包括通过与不通过机型，重点是不通过机型及原因;"  
)


# # Run the agent
async def main():
    result = await agent7.ainvoke(
        {"messages": [{"role": "user", "content": "执行版本V5.85.10.10F25.08050447健康度测试，我的信息是陶森10306639"}]}
    )
    final_response = result['messages'][-1].content
    print(final_response)


if __name__ == "__main__":
    asyncio.run(main())


