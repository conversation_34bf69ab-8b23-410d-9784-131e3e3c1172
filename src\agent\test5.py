from langgraph.prebuilt import create_react_agent
from langchain_core.tools import tool
import requests
import json
import aiohttp
import asyncio
# 本地大模型配置
# LOCAL_LLM_CONFIG = {
#     "api_key": "anything",
#     "base_url": "http://10.57.84.90:4000",
#     "model": "myqwen3",
# }
LOCAL_LLM_CONFIG = {
    "api_key": "anything",
    "base_url": "http://10.231.145.183:10900/iask/v8",
    "model": "Qwen3-235B-A22B-FP8",
}
def get_langchain_model():
    """获取配置好的LangChain模型"""
    from langchain_openai import ChatOpenAI
    return ChatOpenAI(
        api_key=LOCAL_LLM_CONFIG["api_key"],
        base_url=LOCAL_LLM_CONFIG["base_url"],
        model=LOCAL_LLM_CONFIG["model"],
        temperature=0.7,
        timeout=600
    )


llm = get_langchain_model()
# #工具
# def usecasegenerate(question,peopleinfo):
#     """测试用例生成
#     question 用户输入 输入需求ID，一般为 ZXPFM-XXXXXX，默认需求ID后面加上 ,自动化
#     peopleinfo 用户信息 一般为姓名工号
    
#     返回生成的测试用例
#     """
#     print("333:",question+"   "+peopleinfo)
#     url = "https://studio.zte.com.cn/zte-studio-ai-platform/openapi/v1/chat"
#     headers = {"Authorization": "Bearer ab092c4a74264afd8f9cf6f3186e1ab6-66acf3607e8942d2a2d8296e45c11e7c",
#                "X-Emp-No": "10306639",
#                "X-Auth-Value": "eb2c3782f01589fc50831f389a69e5d4",
#                "Content-Type": "application/json"}
#     data = {
#         "chatUuid": "",
#         "chatName": "",
#         "stream": "false",
#         "keep": "True",
#         "text": question+"\r\n"+peopleinfo,
#         "model": "nebulacoder"
#     }
#     response = requests.post(url, json=data, headers=headers)
#     print("444:",response.text)

#     response.raise_for_status()  # 检查请求是否成功
#     return response.text  # 返回响应内容  

# def usecaseimport(question,peopleinfo):
#     """测试用例导入
#     question 测试用例生成的全量用例
#     peopleinfo 用户信息 一般为姓名工号
#     """
#     print("111:",question+"   "+peopleinfo)
#     url = "https://studio.zte.com.cn/zte-studio-ai-platform/openapi/v1/chat"
#     headers = {"Authorization": "Bearer ddc07a1529d844a6bf6a5ca33484fe84-ef09b94ff8154fbb86bc6adb38d668a2",
#                "X-Emp-No": "10306639",
#                "X-Auth-Value": "eb2c3782f01589fc50831f389a69e5d4",
#                "Content-Type": "application/json"}
#     data = {
#         "chatUuid": "",
#         "chatName": "",
#         "stream": "false",
#         "keep": "True",
#         "text": question+"\r\n"+peopleinfo,
#         "model": "nebulacoder"
#     }
#     response = requests.post(url, json=data, headers=headers)
#     print("222:",response.text)
#     response.raise_for_status()  # 检查请求是否成功
#     return response.text  # 返回响应内容  

@tool
async def usecasegenerate(question, peopleinfo):
    """
    异步生成测试用例
    question 用户输入 输入需求ID，一般为 ZXPFM-XXXXXX，默认需求ID后面加上 ,自动化
    peopleinfo 用户信息 一般为姓名工号
    
    返回生成的测试用例
    """
    headers = {"Authorization": "Bearer ab092c4a74264afd8f9cf6f3186e1ab6-66acf3607e8942d2a2d8296e45c11e7c",
               "X-Emp-No": "10306639",
               "X-Auth-Value": "eb2c3782f01589fc50831f389a69e5d4",
               "Content-Type": "application/json"}
    data = {
        "chatUuid": "",
        "chatName": "",
        "stream": "false",
        "keep": "True",
        "text": question+"\r\n"+peopleinfo,
        "model": "nebulacoder"
    }
    
    url = "https://studio.zte.com.cn/zte-studio-ai-platform/openapi/v1/chat"
    
    # 创建异步HTTP客户端会话
    async with aiohttp.ClientSession() as session:
        try:
            # 发送异步POST请求
            async with session.post(url, json=data, headers=headers) as response:
                response.raise_for_status()  # 检查请求是否成功
                # 异步读取响应文本并返回
                return await response.text()
        except aiohttp.ClientError as e:
            # 捕获HTTP相关异常（连接错误、超时、状态码错误等）
            return f"请求出错: {str(e)}"
        except Exception as e:
            # 捕获其他异常
            return f"生成测试用例失败: {str(e)}"
        
@tool
async def usecaseimport(question, peopleinfo):
    """
    异步导入测试用例
    question 测试用例生成的全量用例
    peopleinfo 用户信息 一般为姓名工号
    """
    # 保留原打印逻辑
    print(f"111: {question}   {peopleinfo}")
    headers = {"Authorization": "Bearer ddc07a1529d844a6bf6a5ca33484fe84-ef09b94ff8154fbb86bc6adb38d668a2",
               "X-Emp-No": "10306639",
               "X-Auth-Value": "eb2c3782f01589fc50831f389a69e5d4",
               "Content-Type": "application/json"}
    data = {
        "chatUuid": "",
        "chatName": "",
        "stream": "false",
        "keep": "True",
        "text": question+"\r\n"+peopleinfo,
        "model": "nebulacoder"
    }
    url = "https://studio.zte.com.cn/zte-studio-ai-platform/openapi/v1/chat"
    
    # 异步HTTP会话管理
    async with aiohttp.ClientSession() as session:
        try:
            # 发送异步POST请求
            async with session.post(url, json=data, headers=headers) as response:
                # 读取响应内容
                response_text = await response.text()
                # 保留原打印逻辑
                print(f"222: {response_text}")
                # 检查响应状态码
                response.raise_for_status()
                return response_text
        except aiohttp.ClientError as e:
            print(f"HTTP请求错误: {str(e)}")
            return f"导入失败: {str(e)}"
        except Exception as e:
            print(f"处理错误: {str(e)}")
            return f"导入失败: {str(e)}"

agent5 = create_react_agent(
    model=llm,  
    tools=[usecasegenerate,usecaseimport],  
    prompt="你是一个优秀的用例设计人员。通常的步骤是先进行测试用例生成，将生成的测试用例全量导入,无需人工确认。"  
)
# async def main():
#     result = await agent.ainvoke(
#         {"messages": [{"role": "user", "content": "我想设计用例，需求ID是ZXPFM-173921,我的信息是陶森10306639"}]}
#     )
#     final_response = result['messages'][-1].content
#     print(final_response)

    
# if __name__ == "__main__":
#     asyncio.run(main())


