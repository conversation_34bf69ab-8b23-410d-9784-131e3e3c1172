#环境匹配测试执行
from langgraph.prebuilt import create_react_agent
from langchain_core.tools import tool
import requests
import json
import aiohttp
import asyncio
from langchain_core.messages.utils import (
    trim_messages, 
    count_tokens_approximately
)
# 本地大模型配置
# LOCAL_LLM_CONFIG = {
#     "api_key": "anything",
#     "base_url": "http://10.57.84.90:4000",
#     "model": "myqwen3",
# }

def pre_model_hook(state):
    trimmed_messages = trim_messages(
        state["messages"],
        strategy="last",
        token_counter=count_tokens_approximately,
        max_tokens=64000,
        start_on="human",
        end_on=("human", "tool"),
    )
    return {"llm_input_messages": trimmed_messages}

LOCAL_LLM_CONFIG = {
    "api_key": "anything",
    "base_url": "http://10.231.145.183:10900/iask/v8",
    "model": "Qwen3-235B-A22B-FP8",
}
def get_langchain_model():
    """获取配置好的LangChain模型"""
    from langchain_openai import ChatOpenAI
    return ChatOpenAI(
        api_key=LOCAL_LLM_CONFIG["api_key"],
        base_url=LOCAL_LLM_CONFIG["base_url"],
        model=LOCAL_LLM_CONFIG["model"],
        temperature=0.7,
        timeout=600
    )


llm = get_langchain_model()

# #工具
# def envmatch(systemid):
#     """环境匹配
#     systemid 用户输入 输入用例ID，一般为 ZXPFM-XXXXXX，仅保留XXXXXX部分
    
#     返回 queuelen及队列最小的 jobid及模板任务号 envid及环境编号
#     """
#     print("333:",systemid)
#     url = "https://iwork.zx.zte.com.cn/iWork2UseCase/QueryMatchInfoServlet?id="+systemid+""
#     response = requests.post(url)
#     print("444:",response.text)

#     response.raise_for_status()  # 检查请求是否成功
#     return response.text  # 返回响应内容  
#工具

@tool
async def envmatch(systemid):
    """环境匹配
    systemid 用户输入 输入用例ID，一般为 ZXPFM-XXXXXX，仅保留XXXXXX部分
    
    返回 queuelen及队列最小的 jobid及模板任务号 envid及环境编号
    """    
    url = f"https://iwork.zx.zte.com.cn/iWork2UseCase/QueryMatchInfoServlet?id={systemid}"
    print   ("333:", systemid)
    print   ("333:", url)
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(url) as response:
                response.raise_for_status()
                return await response.text()
        except aiohttp.ClientError as e:
            print(f"请求出错: {str(e)}")
            return None

@tool    
async def copyjobid(jobid, number, systemid, envid):
    """
    异步复制测试任务并将测试任务运行
    jobid 模板任务号
    number 用户信息 一般为姓名工号
    systemid 输入用例ID，一般为 ZXPFM-XXXXXX，仅保留XXXXXX部分
    envid 环境的编号

    测试任务生成后调用执行接口，进行测试任务的执行。返回模板任务号，执行环境、当前状态、复制出来的任务号
    """    
    url = f"https://iwork.zx.zte.com.cn/iWork2Use/JobCopyServlet3?usecaseid={systemid}&jobid={jobid}&envid={envid}&people={number}"
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(url) as response:
                response.raise_for_status()
                response_text = await response.text()
                tempdata = json.loads(response_text)
                
                url2 = f"https://iwork.zx.zte.com.cn/iWork2Use/JobJoinPoolServlet?number={number[-8:]}&jobid={str(tempdata[0])}&people={number[:-8]}"
                async with session.post(url2) as response2:
                    response2.raise_for_status()
                    return f"{response_text}测试任务已加入队列"
        
        except aiohttp.ClientError as e:
            return f"请求出错: {str(e)}"
        except json.JSONDecodeError as e:
            return f"响应解析失败: {str(e)}"
        except Exception as e:
            return f"操作失败: {str(e)}"

# def copyjobid(jobid,number,systemid,envid):

#     """复制测试任务并将测试任务运行
#     jobid 模板任务号
#     number 用户信息 一般为姓名工号
#     systemid 输入用例ID，一般为 ZXPFM-XXXXXX，仅保留XXXXXX部分
#     envid 环境的编号

#     测试任务生成后调用执行接口，进行测试任务的执行
#     """    
#     url = "https://iwork.zx.zte.com.cn/iWork2Use/JobCopyServlet3?usecaseid="+systemid+"&jobid="+jobid+"&envid="+envid+"&people="+number
#     response = requests.post(url)
#     tempdata =json.loads(response.text)
#     url2 = "https://iwork.zx.zte.com.cn/iWork2Use/JobJoinPoolServlet?number="+ number[-8:]+"&jobid="+str(tempdata[0])+"&people="+number[:-8]
#     response2 = requests.post(url2)
#     response2.raise_for_status()  # 检查请求是否成功

#     return response.text+"测试任务已加入队列"  # 返回响应内容  

agent6 = create_react_agent(
    model=llm,  
    tools=[envmatch,copyjobid],  
    pre_model_hook=pre_model_hook,
    prompt="你擅长进行环境匹配以及测试任务执行。多个用例分开进行处理，通常的步骤是测试用例先进行环境匹配，返回队列最短的模板任务，若存在则对模板任务进行复制，并对复制出来的测试任务进行执行；若不存在，则返回无匹配测试模板;"  
)

# Run the agent
# async def main():
#     result = await agent.ainvoke(
#         {"messages": [{"role": "user", "content": "执行测试用例ZXPFM-238516,ZXPFM-238515，我的信息是陶森10306639"}]}
#     )
#     final_response = result['messages'][-1].content
#     print(final_response)


# if __name__ == "__main__":
#     asyncio.run(main())


