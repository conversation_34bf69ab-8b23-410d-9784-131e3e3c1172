
import asyncio
from typing import Annotated, Optional
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.prebuilt import create_react_agent
from langchain_core.messages import HumanMessage
from langgraph.graph import StateGraph
from langgraph.graph.message import add_messages
from typing_extensions import TypedDict
import httpx

# 全局变量存储工具
_mcp_tools: Optional[list] = None
_mcp_client: Optional[MultiServerMCPClient] = None

class State(TypedDict):
    messages: Annotated[list, add_messages]

class Configuration:
    """Configuration for the agent."""
    pass

# 本地大模型配置
LOCAL_LLM_CONFIG = {
    "api_key": "anything",
    "base_url": "http://10.231.145.183:10900/iask/v8",
    "model": "Qwen3-235B-A22B-FP8",
}

def get_langchain_model():
    """获取配置好的LangChain模型"""
    from langchain_openai import ChatOpenAI
    return ChatOpenAI(
        api_key=LOCAL_LLM_CONFIG["api_key"],
        base_url=LOCAL_LLM_CONFIG["base_url"],
        model=LOCAL_LLM_CONFIG["model"],
        temperature=0.7,
        timeout=600
    )

async def get_mcp_tools_async():
    """异步获取MCP工具"""
    global _mcp_tools, _mcp_client

    if _mcp_tools is None:
        try:
            # 只配置SSE传输的服务器，避免Windows下的stdio问题
            _mcp_client = MultiServerMCPClient({
                "reportapi": {
                    "url": "https://wxiot.zte.com.cn/reportapi/mcp",
                    "transport": "sse"
                }
            })
            _mcp_tools = await _mcp_client.get_tools()

        except Exception as e:
            # 如果MCP工具加载失败，返回空列表
            _mcp_tools = []

    return _mcp_tools

def get_mcp_tools():
    """同步获取MCP工具 - 仅用于图初始化"""
    try:
        return asyncio.run(get_mcp_tools_async())
    except Exception as e:
        return []

# 创建图实例
tools = get_mcp_tools()
model = get_langchain_model()
graph = create_react_agent(
    model=model,
    tools=tools,
    prompt="You are a helpful assistant"
)



